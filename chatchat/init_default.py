import sqlite3
from typing import Optional

def create_connection() -> Optional[sqlite3.Connection]:
    local_db = 'platform_management.db'

    """创建数据库连接

    Args:
        db_file: 数据库文件路径

    Returns:
        sqlite3.Connection对象或None(如果连接失败)
    """
    try:
        conn = sqlite3.connect(local_db)
        conn.row_factory = sqlite3.Row  # 设置为Row工厂，这样查询结果可以按列名访问
        return conn
    except sqlite3.Error as e:
        # logger.error(f"数据库连接失败: {e}")
        return None