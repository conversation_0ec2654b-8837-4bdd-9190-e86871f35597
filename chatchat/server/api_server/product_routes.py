from chatchat.server.db import db_handler
from chatchat.server.knowledge_base.kb_api import create_kb
from chatchat.server.knowledge_base.kb_doc_api import update_docs
from chatchat.server.knowledge_base.kb_service.base import KBServiceFactory
from chatchat.server.knowledge_base.utils import shop_product_write_file
from chatchat.server.types.server.response.base import BaseResponse
from fastapi import APIRouter
from pydantic import BaseModel

from chatchat.server.utils import get_default_embedding
from chatchat.settings import Settings

product_router = APIRouter(prefix="/product", tags=["Product Base Management"])


class InnerItem(BaseModel):
    standard: None | str
    description: None | str
    amount: None | int
    single_price: None | float
    group_price: None | float
    reference_price: None | float
    image: None | str


@product_router.post("/add_product", response_model=BaseResponse, summary="")
def add_product(data: list[dict], knowledge_base=None) -> BaseResponse:
    if not data or len(data) == 0:
        return BaseResponse.error(f"数据为空")

    shop_name = data[0].get('shop_name', None)
    platform = data[0].get('platform', None)
    if not all([shop_name, platform]):
        return BaseResponse.error(f'shop_name:{shop_name} platform:{platform} 数据缺失')

    if knowledge_base is None:
        knowledge_base = f"{platform}_{shop_name}"

    # 是否存在
    kb = KBServiceFactory.get_service_by_alias(knowledge_base)
    if kb is None:
        res = create_kb(knowledge_base_name=knowledge_base, kb_info=f"{knowledge_base}的知识库",
                        vector_store_type=Settings.kb_settings.DEFAULT_VS_TYPE,
                        embed_model=get_default_embedding())
        if res.code != 200:
            return res
        kb = KBServiceFactory.get_service_by_alias(knowledge_base)

    knowledge_base = kb.kb_name

    result = db_handler.add_product(data)
    if isinstance(result, str):  # 明确判断类型
        return BaseResponse.error(f"获取失败: {result}")

    file_name = shop_product_write_file(data,knowledge_base)
    # 更新知识库
    result = update_docs(
        knowledge_base_name=knowledge_base,
        file_names=[file_name],
        chunk_size=Settings.kb_settings.CHUNK_SIZE,  # 调整聊天问题对应的值
        chunk_overlap=Settings.kb_settings.OVERLAP_SIZE,
        zh_title_enhance=True,
        override_custom_docs=True,
        not_refresh_vs_cache=False,
        docs=''
    )

    return result