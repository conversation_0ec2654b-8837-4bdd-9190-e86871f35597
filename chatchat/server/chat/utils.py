import logging
from functools import lru_cache
from typing import Dict, List, Tuple, Union
import json
from langchain.callbacks import AsyncIteratorCallbackHandler
from langchain.prompts.chat import ChatMessagePromptTemplate

from chatchat.server.pydantic_v2 import BaseModel, Field
from chatchat.utils import build_logger


logger = build_logger()


class History(BaseModel):
    """
    对话历史
    可从dict生成，如
    h = History(**{"role":"user","content":"你好"})
    也可转换为tuple，如
    h.to_msy_tuple = ("human", "你好")
    """

    role: str = Field(...)
    content: str = Field(...)

    def to_msg_tuple(self):
        return "ai" if self.role == "assistant" else "human", self.content

    def to_msg_template(self, is_raw=True) -> ChatMessagePromptTemplate:
        role_maps = {
            "ai": "assistant",
            "human": "user",
        }
        role = role_maps.get(self.role, self.role)
        if is_raw:  # 当前默认历史消息都是没有input_variable的文本。
            content = "{% raw %}" + self.content + "{% endraw %}"
        else:
            content = self.content

        return ChatMessagePromptTemplate.from_template(
            content,
            "jinja2",
            role=role,
        )

    @classmethod
    def from_data(cls, h: Union[List, Tuple, Dict]) -> "History":
        if isinstance(h, (list, tuple)) and len(h) >= 2:
            h = cls(role=h[0], content=h[1])
        elif isinstance(h, dict):
            h = cls(**h)

        return h


from typing import Any, Literal

# 处理错误信息专用
class SelfAsyncIteratorCallbackHandler(AsyncIteratorCallbackHandler):
    def __init__(self):
        super().__init__()
        self.error = None

    def parse_open_error(self):
        if self.error is None:
            return None

        error_str = str(self.error)
        if "-" not in error_str:
            self.error = '错误信息:' + error_str # 添加前置错误,用语前端识别
            return None

        try:
            # 分割字符串，提取JSON部分
            json_part = error_str.split("-", 1)[1].strip()
            # 处理单引号问题（JSON标准要求双引号）
            json_part = json_part.replace("'", '"')
            json_part = json_part.replace("None", "null")

            # 解析为字典
            error_dict = json.loads(json_part)
            error_str = f"错误类型:{error_dict['error']['code']}, 信息:{error_dict['error']['message']}"
        except json.JSONDecodeError as e:
            logger.error(str(e) + error_str)

        # 添加前置错误,用语前端识别
        self.error = '错误信息:' + error_str
        logger.error(self.error)  # 原始错误已有打印
        return None

    async def on_llm_error(self, error: BaseException, **kwargs: Any) -> None:
        self.error = str(error)  # 保存错误信息
        self.parse_open_error()
        self.done.set()