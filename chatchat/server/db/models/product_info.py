from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Integer, String, func
from chatchat.server.db.base import Base


class ProductInfoModel(Base):
    """
    产品信息模型
    """

    __tablename__ = "product_info"
    id = Column(Integer, primary_key=True, autoincrement=True, comment="id")
    platform = Column(String, comment="")
    product_name = Column(String, comment="")
    shop_name = Column(String, comment="")
    product_id = Column(String, comment="")
    platform_shop_id = Column(Integer, comment="")
    product_type = Column(String, comment="")
    images = Column(String, comment="")
    description = Column(String, comment="")
    share_text = Column(String, comment="")
    url = Column(String, comment="")
    attrs = Column(String, comment="")
    is_update_knowledge = Column(Integer,default=0, comment="")
    is_delete = Column(Integer,default=0, comment="")
    add_time = Column(Integer, comment="")
    update_time = Column(Integer, comment="")

    def __repr__(self):
        return (f"<product_info('{self.id}','{self.platform}','{self.attrs}','{self.shop_name}','{self.platform_shop_id}','{self.product_type}','{self.images}','{self.description}','{self.share_text}','{self.url}','{self.is_update_knowledge}','{self.is_delete}','{self.add_time}','{self.update_time}')>")
