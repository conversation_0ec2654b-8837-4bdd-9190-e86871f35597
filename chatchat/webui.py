import sys

import streamlit as st
import streamlit_antd_components as sac

from chatchat import __version__
from chatchat.server.utils import api_address
from chatchat.webui_pages.chat_history import chat_history
from chatchat.webui_pages.kb_chat import kb_chat
from chatchat.webui_pages.knowledge_base.knowledge_base import knowledge_base_page
from chatchat.webui_pages.prompt_setting import prompt_setting
from chatchat.webui_pages.shop_setting import shop_setting
from chatchat.webui_pages.system_seting import system_setting
from chatchat.webui_pages.utils import *

api = ApiRequest(base_url=api_address())

if __name__ == "__main__":
    is_lite = "lite" in sys.argv  # TODO: remove lite mode

    # 设置页面配置
    st.set_page_config(
        page_title="深维智眸 - 智能客服系统",
        page_icon="🤖",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # 科技感高交互设计样式 - 浅色简约风格
    st.markdown(
        """
        <style>
        [data-testid="stSidebarUserContent"] {
            padding-top: 0px;
        }
        .block-container {
            padding-top: 25px;
        }
        [data-testid="stBottomBlockContainer"] {
            padding-bottom: 20px;
        }
        """,
        unsafe_allow_html=True,
    )

    # 检查登录状态
    is_logged_in = api.check_login()
    # 禁用菜单如果未登录
    # 登录/登出按钮
    if not is_logged_in:
        st.warning("当前未登录或者已过期，请到客户端进行登录操作")
        # 未登录时禁用所有功能
        selected_page = None
        st.stop()

    # 侧边栏导航
    with st.sidebar:
        # 系统标题和版本信息
        st.markdown(
            f"""
            <div class="sidebar-title">
                🤖 深维智眸
            </div>
            <div style="text-align: center; color: #64748b; font-size: 0.9rem; margin-bottom: 2rem;">
                智能客服系统 v{__version__}
            </div>
            """,
            unsafe_allow_html=True
        )

        # 导航菜单
        selected_page = sac.menu(
            [
                sac.MenuItem("智能对话", icon="chat-dots", description="AI智能客服对话"),
                sac.MenuItem("知识库管理", icon="database-gear", description="管理知识库内容"),
                sac.MenuItem("聊天记录", icon="chat-left-text", description="查看历史对话"),
                sac.MenuItem("客服模板", icon="clipboard-data", description="管理客服模板"),
                sac.MenuItem("商店配置", icon="shop", description="商店相关设置"),
                sac.MenuItem("系统设置", icon="gear", description="系统参数配置"),
            ],
            key="selected_page",
            open_index=0,
            size="large",
        )

        sac.divider()

    if selected_page == "知识库管理":
        knowledge_base_page(api=api, is_lite=is_lite)
    elif selected_page == "智能对话":
        kb_chat(api=api)
    elif selected_page == "聊天记录":
        chat_history(api=api)
    elif selected_page == "系统设置":
        system_setting(api=api)
    elif selected_page == "客服模板":
        prompt_setting(api=api)
    elif selected_page == "商店配置":
        shop_setting(api=api)
    else:
        kb_chat(api=api)  # 默认页面
