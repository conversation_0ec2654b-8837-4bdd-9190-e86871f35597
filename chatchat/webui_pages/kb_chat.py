from datetime import datetime
import uuid
from typing import List, Dict

import openai
import streamlit as st
import streamlit_antd_components as sac
from streamlit_chatbox import *
from streamlit_extras.bottom_container import bottom

from chatchat.server.db.db_handler import get_platform_prompt_name
from chatchat.server.knowledge_base.kb_service.base import get_kb_details

from chatchat.server.knowledge_base.utils import LOADER_DICT
from chatchat.server.utils import get_config_models, get_config_platforms, get_default_llm, api_address, get_default_kb
from chatchat.webui_pages.dialogue.dialogue import (save_session, restore_session, rerun,
                                                    get_messages_history, upload_temp_docs,
                                                    add_conv, del_conv, clear_conv)
from chatchat.webui_pages.utils import *


chat_box = ChatBox(assistant_avatar=get_img_base64("chatchat_icon_blue_square_v2.png"))


def init_widgets():
    st.session_state.setdefault("history_len", Settings.model_settings.HISTORY_LEN)
    st.session_state.setdefault("selected_kb", get_default_kb())
    st.session_state.setdefault("kb_top_k", Settings.kb_settings.VECTOR_SEARCH_TOP_K)
    st.session_state.setdefault("se_top_k", Settings.kb_settings.SEARCH_ENGINE_TOP_K)
    st.session_state.setdefault("score_threshold", Settings.kb_settings.SCORE_THRESHOLD)
    st.session_state.setdefault("search_engine", Settings.kb_settings.DEFAULT_SEARCH_ENGINE)
    st.session_state.setdefault("return_direct", False)
    st.session_state.setdefault("cur_conv_name", chat_box.cur_chat_name)
    st.session_state.setdefault("last_conv_name", chat_box.cur_chat_name)
    st.session_state.setdefault("file_chat_id", None)


def kb_chat(api: ApiRequest):
    ctx = chat_box.context
    ctx.setdefault("uid", uuid.uuid4().hex)
    ctx.setdefault("file_chat_id", None)
    ctx.setdefault("temperature", Settings.model_settings.TEMPERATURE)
    init_widgets()
    kb_top_k = Settings.kb_settings.VECTOR_SEARCH_TOP_K
    # sac on_change callbacks not working since st>=1.34
    if st.session_state.cur_conv_name != st.session_state.last_conv_name:
        save_session(st.session_state.last_conv_name)
        restore_session(st.session_state.cur_conv_name)
        st.session_state.last_conv_name = st.session_state.cur_conv_name

    llm_model = get_default_llm()

    # 配置参数
    try:
        kb_details = get_kb_details()
        kb_alias2name_list = {x["kb_alias"]: x["kb_name"] for x in kb_details}
    except Exception as e:
        st.error(
            "获取知识库信息错误，请检查是否已按照 `README.md` 中 `4 知识库初始化与迁移` 步骤完成初始化或迁移，或是否为数据库连接错误。"
        )
        st.stop()

    kb_list = [x["kb_alias"] for x in kb_details]
    with st.sidebar:
        # tabs = st.tabs(["RAG 配置", "会话设置"])
        # with tabs[0]:
        dialogue_modes = ["知识库问答",
                          "文件对话",
                          "搜索引擎问答",
                          ]
        dialogue_mode = st.selectbox("请选择对话模式：",
                                     dialogue_modes,
                                     key="dialogue_mode",
                                     )
        placeholder = st.empty()
        prompt_templates_kb_list = get_platform_prompt_name('rag')  # list(Settings.prompt_settings.rag)
        prompt_name = st.selectbox(
            "请选择客服模板：",
            prompt_templates_kb_list,
            key="prompt_name",
        )

        platforms = ["所有"] + list(get_config_platforms())
        platform = st.selectbox("选择模型平台", platforms, key="platform")
        llm_models = list(
            get_config_models(
                model_type="llm", platform_name=None if platform == "所有" else platform
            )
        )
        llm_models += list(
            get_config_models(
                model_type="image2text", platform_name=None if platform == "所有" else platform
            )
        )
        llm_model = st.selectbox("选择LLM模型", llm_models, key="llm_model")

        def on_kb_change():
            st.toast(f"已加载知识库： {st.session_state.selected_kb}")

        with placeholder.container():
            if dialogue_mode == "知识库问答":
                selected_kb = st.selectbox(
                    "请选择知识库：",
                    kb_list,
                    on_change=on_kb_change,
                    key="selected_kb",
                )
            elif dialogue_mode == "文件对话":
                files = st.file_uploader("上传知识文件：",
                                         [i for ls in LOADER_DICT.values() for i in ls],
                                         accept_multiple_files=True,
                                         )
                if st.button("开始上传", disabled=len(files) == 0):
                    st.session_state["file_chat_id"] = upload_temp_docs(files, api)
            elif dialogue_mode == "搜索引擎问答":
                search_engine_list = list(Settings.tool_settings.search_internet["search_engine_config"])
                search_engine = st.selectbox(
                    label="请选择搜索引擎",
                    options=search_engine_list,
                    key="search_engine",
                )

    # Display chat messages from history on app rerun
    chat_box.output_messages()
    chat_input_placeholder = "请输入对话内容，换行请使用Shift+Enter。"

    # chat input
    with bottom():
        cols = st.columns([1, 0.2, 15, 1])
        # if cols[0].button(":gear:", help="模型配置"):
        #     widget_keys = ["platform", "llm_model", "temperature", "system_message"]
        #     chat_box.context_to_session(include=widget_keys)
        if cols[-1].button(":wastebasket:", help="清空对话"):
            chat_box.reset_history()
            rerun()
        # with cols[1]:
        #     mic_audio = audio_recorder("", icon_size="2x", key="mic_audio")
        prompt = cols[2].chat_input(chat_input_placeholder, key="prompt")
    if prompt:
        history = get_messages_history(st.session_state.get('history_len'))
        messages = history + [{"role": "user", "content": prompt}]
        chat_box.user_say(prompt)

        extra_body = dict(
            top_k=kb_top_k,
            score_threshold=st.session_state.score_threshold,
            temperature=ctx.get("temperature"),
            prompt_name=prompt_name,
            return_direct=st.session_state.return_direct,
        )

        api_url = api_address(is_public=True)
        if dialogue_mode == "知识库问答":
            client = openai.Client(base_url=f"{api_url}/knowledge_base/local_kb/{kb_alias2name_list[selected_kb]}", api_key="NONE")
            chat_box.ai_say([
                Markdown("...", in_expander=True, title="知识库匹配结果", state="running", expanded=st.session_state.return_direct),
                f"正在查询知识库 `{selected_kb}` ...",
            ])
        elif dialogue_mode == "文件对话":
            if st.session_state.get("file_chat_id") is None:
                st.error("请先上传文件再进行对话")
                st.stop()
            knowledge_id = st.session_state.get("file_chat_id")
            client = openai.Client(base_url=f"{api_url}/knowledge_base/temp_kb/{knowledge_id}", api_key="NONE")
            chat_box.ai_say([
                Markdown("...", in_expander=True, title="知识库匹配结果", state="running", expanded=st.session_state.return_direct),
                f"正在查询文件 `{st.session_state.get('file_chat_id')}` ...",
            ])
        else:
            client = openai.Client(base_url=f"{api_url}/knowledge_base/search_engine/{search_engine}", api_key="NONE")
            chat_box.ai_say([
                Markdown("...", in_expander=True, title="知识库匹配结果", state="running", expanded=st.session_state.return_direct),
                f"正在执行 `{search_engine}` 搜索...",
            ])

        text = ""
        first = True

        try:
            for d in client.chat.completions.create(messages=messages, model=llm_model, stream=True,
                                                    extra_body=extra_body):
                if first:
                    chat_box.update_msg("\n\n".join(d.docs), element_index=0, streaming=False, state="complete")
                    chat_box.update_msg("", streaming=False)
                    first = False
                    continue
                text += d.choices[0].delta.content or ""
                chat_box.update_msg(text.replace("\n", "\n\n"), streaming=True)
            chat_box.update_msg(text, streaming=False)
            # TODO: 搜索未配置API KEY时产生报错
        except Exception as e:
            st.error(e.body)