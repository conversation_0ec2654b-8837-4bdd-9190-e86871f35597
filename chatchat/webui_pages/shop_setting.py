import datetime
import random
import time

import pandas as pd
import streamlit as st

from chatchat.server.db.db_handler import get_platform_prompt_name
from chatchat.server.knowledge_base.kb_api import list_kbs
from chatchat.webui_pages.utils import ApiRequest


def shop_setting(api):
    st.subheader("店铺管理")

    # Initialize default values
    default_per_page = 10
    if 'shop_page' not in st.session_state:
        st.session_state.shop_page = 1
    if 'shop_sort_column' not in st.session_state:
        st.session_state.shop_sort_column = "update_time"  # 默认按创建时间排序
    if 'shop_sort_direction' not in st.session_state:
        st.session_state.shop_sort_direction = "desc"  # 默认降序

    # Initialize session state
    session_defaults = {
        'page': 1,
        'last_edited': {},
        'show_add_form': False,
        'selected_rows': set(),
        'editor_key': str(random.randint(0, 1000000))
    }

    for key, default in session_defaults.items():
        if key not in st.session_state:
            st.session_state[key] = default

    # 缓存获取函数
    def _get_base_data():
        """获取基本数据并缓存"""
        prompt_list = get_platform_prompt_name('rag')
        kb_list = [v.kb_name for v in list_kbs().data]
        return {
            'prompt_list': prompt_list,
            'kb_list': kb_list,
            'prompt_set': set(prompt_list),
            'kb_set': set(kb_list)
        }

    # 获取数据函数
    def get_data(page, per_page, sort_column, sort_direction):
        """获取分页数据"""
        base_data = _get_base_data()
        res = api.get_shop_knowledge(sort_column, sort_direction,page=page, per_page=per_page)

        value = []
        for v in res['shop_knowledge']:
            kb_valid = v['knowledge_base'] in base_data['kb_set']
            prompt_valid = v['prompt_name'] in base_data['prompt_set']

            value.append({
                "id": v['id'],
                "商户id": v['business_id'],
                "选择": False,
                "店铺": v['shop_name'],
                "知识库": f"{v['knowledge_base']}{'(已被删除)' if not kb_valid else ''}",
                "客服模板": f"{v['prompt_name']}{'(已被删除)' if not prompt_valid else ''}",
                "简介": v['description'],
                "更新时间": datetime.datetime.fromtimestamp(v['update_time']).strftime("%Y-%m-%d %H:%M:%S")
                if v.get('update_time') else '未知时间',
            })
        return value, res.get('total', 0)

    # 保存修改的函数
    def save_changes(changed_rows):
        if changed_rows.empty:
            st.warning("没有检测到实际修改的内容")
            return

        with st.spinner(f"正在保存 {len(changed_rows)} 条修改..."):
            success_count = 0
            error_messages = []

            for _, row in changed_rows.iterrows():
                try:
                    # 确保获取到ID
                    row_id = int(row['id']) if pd.notna(row['id']) else None
                    if not row_id:
                        error_messages.append("错误: 缺少ID，无法保存修改")
                        continue

                    # 清理字段中的标记
                    knowledge_base = str(row['知识库']).replace('(已被删除)', '').strip()
                    prompt_name = str(row['客服模板']).replace('(已被删除)', '').strip()

                    result = api.set_shop_knowledge(
                        shop_knowledge_id=row_id,  # 确保传递ID
                        shop_name=str(row['店铺']),
                        prompt_name=prompt_name,
                        knowledge_base=knowledge_base,
                        description=str(row['简介']),
                    )

                    if result.get('code') == 200:
                        success_count += 1
                        st.session_state.last_edited[row_id] = {
                            '商户id': row['商户id'],
                            '店铺': row['店铺'],
                            '知识库': knowledge_base,
                            '客服模板': prompt_name,
                            '简介': row['简介']
                        }
                    else:
                        error_messages.append(f"ID:{row_id}: {result.get('msg', '未知错误')}")
                except Exception as e:
                    error_messages.append(f"ID:{row_id if 'row_id' in locals() else '未知'}: {str(e)}")

            for msg in error_messages:
                st.error(msg)

            if success_count > 0:
                st.success(f"成功保存 {success_count} 条修改")
                _clear_cache_and_rerun()

    # 删除函数
    def delete_selected_prompts(selected_ids):
        if not selected_ids:
            st.warning("请先选择要删除的模板")
            return

        with st.spinner("正在删除选中的模板..."):
            try:
                result = api.del_shop_knowledge(selected_ids)
                if result.get('code') == 200:
                    for id in selected_ids:
                        st.session_state.last_edited.pop(id, None)
                    st.success(f"成功删除 {len(selected_ids)} 条模板")
                    _clear_cache_and_rerun()
                else:
                    st.error(f"删除失败: {result.get('msg', '未知错误')}")
            except Exception as e:
                st.error(f"删除异常: {str(e)}")

    # 清理缓存并刷新
    def _clear_cache_and_rerun():
        st.cache_data.clear()
        st.session_state.editor_key = str(random.randint(0, 1000000))
        time.sleep(1)
        st.rerun()

    # 获取变更的行
    def _get_changed_rows(original_df, edited_df):
        changed_rows = []
        for idx, edited_row in edited_df.iterrows():
            original_row = original_df.iloc[idx]
            if any(str(edited_row[col]) != str(original_row[col])
                   for col in ['商户id', '店铺', '知识库', '客服模板', '简介']):
                changed_row = edited_row.copy()
                changed_row['id'] = original_row['id']  # 确保ID被传递
                changed_rows.append(changed_row)
        return pd.DataFrame(changed_rows) if changed_rows else pd.DataFrame()

    with st.sidebar:
        sort_column = st.selectbox(
            "排序字段",
            options=[
                "update_time",
                "shop_name",
                "knowledge_base",
                "prompt_name",
            ],
            format_func=lambda x: {
                "update_time": "更新时间",
                "shop_name": "店铺",
                "knowledge_base": "知识库",
                "prompt_name": "客服模板",
            }.get(x, x),
            index=0,
            key="shop_sort_column_select"
        )
        sort_direction = st.selectbox(
            "排序方向",
            options=["desc", "asc"],
            format_func=lambda x: "降序" if x == "desc" else "升序",
            index=0,
            key="shop_sort_direction_select"
        )

    # 主界面逻辑
    def render_main_interface():
        data, total_count = get_data(st.session_state.shop_page, default_per_page, sort_column, sort_direction)
        original_df = pd.DataFrame(data)

        # 应用编辑状态
        if st.session_state.last_edited:
            for id, edits in st.session_state.last_edited.items():
                mask = original_df['id'] == id
                if mask.any():
                    for col in edits:
                        original_df.loc[mask, col] = edits[col]

        # 显示数据编辑器
        edited_df = st.data_editor(
            original_df,
            column_config={
                "选择": st.column_config.CheckboxColumn("选择", default=False),
                "id": st.column_config.Column(disabled=True),
                "店铺": st.column_config.TextColumn("店铺"),
                "知识库": st.column_config.TextColumn("知识库"),
                "客服模板": st.column_config.TextColumn("客服模板"),
                "简介": st.column_config.TextColumn("简介"),
                "更新时间": st.column_config.Column(disabled=True)
            },
            column_order=["选择", "店铺", "知识库", "客服模板", "简介", "更新时间"],
            hide_index=True,
            use_container_width=True,
            key=st.session_state.editor_key
        )

        # 分页控制
        total_pages = max(1, (total_count + default_per_page - 1) // default_per_page)

        col1, col2, col3 = st.columns([2, 3, 2])
        with col1:
            if st.button("◀ 上一页", disabled=(st.session_state.shop_page == 1), use_container_width=True):
                st.session_state.shop_page -= 1
                _clear_cache_and_rerun()
        with col2:
            st.markdown(
                f"<div style='text-align: center;'>第 {st.session_state.shop_page}/{total_pages} 页 | 共 {total_count} 条</div>",
                unsafe_allow_html=True)
        with col3:
            if st.button("下一页 ▶", disabled=(st.session_state.shop_page >= total_pages), use_container_width=True):
                st.session_state.shop_page += 1
                _clear_cache_and_rerun()
        st.divider()

        # 操作按钮
        selected_ids = edited_df[edited_df['选择']]['id'].tolist() if '选择' in edited_df.columns else []

        col1, col2 = st.columns(2)
        with col1:
            if st.button("💾 保存所有修改", type="primary", use_container_width=True):
                changed_rows = _get_changed_rows(original_df, edited_df)
                save_changes(changed_rows)
        with col2:
            if st.button("🗑️ 删除选中", type="secondary", disabled=not selected_ids, use_container_width=True):
                delete_selected_prompts(selected_ids)

    # 执行主界面渲染
    render_main_interface()
