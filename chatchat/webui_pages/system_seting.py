import datetime
import time

import pandas as pd
import streamlit as st
from chatchat.webui_pages.utils import ApiRequest

def system_setting(api: ApiRequest):
    # 获取原始数据
    st.subheader("大模型平台api key查看")
    st.caption("至少必须得要设置一个大模型平台api key才可用")

    # 获取数据的函数
    def get_data():
        value = []
        res = api.get_platform_info()
        if not res :
            return value
        for v in res:
            value.append({
                "id": v.get('id', ''),
                "平台": v.get('platform_name', '未知'),
                "平台模型类别": v.get('platform_type', '未指定'),
                "api key": v.get('api_key', ''),
                "创建时间": datetime.datetime.fromtimestamp(v['add_time']).strftime("%Y-%m-%d %H:%M:%S") if v.get(
                    'add_time') else '未知时间',
            })
        return value

    # 获取模型数据的函数
    model_dict = {}
    def get_platform_model():
        res = api.get_platform_model()
        value = []
        if isinstance(res, str):
            return value

        model_type_dict = {
            'llm_models': '大语言模型',
            'embed_models': '向量模型',
            '未知': '未知',
        }
        for v in res:
            model_dict[v.get('platform_name', '未知')] = v.get('platform_id', '0')
            value.append({
                "是否启用": v.get('is_set', 0),
                "id": v.get('id', ''),
                "模型平台": v.get('platform_name', '未知'),
                "模型类别": model_type_dict[v.get('model_type', '未指定')],
                "模型Code": v.get('model_code', '未知'),
                "模型名称": v.get('model_name', '未知'),
                "简介": v.get('description', ''),
                "更新时间": datetime.datetime.fromtimestamp(v['update_time']).strftime("%Y-%m-%d %H:%M:%S") if v.get(
                    'update_time') else '未知时间',
            })
        return value

    original_data = get_data()
    original_df = pd.DataFrame(original_data)

    # 显示可编辑表格（只有api key可编辑）
    edited_df = st.data_editor(
        original_df.drop(columns=['id']),
        column_config={
            "平台": st.column_config.Column(disabled=True),
            "平台模型类别": st.column_config.Column(disabled=True),
            "api key": st.column_config.TextColumn("API Key"),
            "创建时间": st.column_config.Column(disabled=True)
        },
        hide_index=True,
        use_container_width=True,
        key="api_key_editor"
    )

    # API Key保存按钮
    if st.button("保存API Key修改", type="primary", key="save_api_key"):
        api_key_changes = []
        for (_, new_row), (_, old_row) in zip(edited_df.iterrows(), original_df.iterrows()):
            if new_row["api key"] != old_row["api key"]:
                api_key_changes.append({
                    "id": old_row["id"],
                    "platform_type": old_row["平台模型类别"],
                    "new_api_key": new_row["api key"]
                })

        if not api_key_changes:
            st.warning("未检测到API Key变更，无需保存")
            return

        with st.status("正在保存API Key变更...", expanded=True) as status:
            success_count = 0
            total_changes = len(api_key_changes)

            for record in api_key_changes:
                try:
                    result = api.set_platform_api_key(
                        record["new_api_key"],
                        record["platform_type"],
                        record["id"]
                    )
                    if result.get('code') == 200:
                        success_count += 1
                    else:
                        st.error(f"API Key更新失败（ID:{record['id']}）: {result.get('message', '未知错误')}")
                except Exception as e:
                    st.error(f"API Key更新异常（ID:{record['id']}）: {str(e)}")

            status.update(
                label=f"API Key保存完成！成功更新 {success_count}/{total_changes} 条记录",
                state="complete" if success_count == total_changes else "error"
            )

            if success_count > 0:
                st.toast(f"API Key更新成功! 1s后自动刷新", icon="✅")
                st.cache_data.clear()
                time.sleep(1)
                st.rerun()

    st.divider()
    st.subheader("大模型平台的模型配置")
    st.caption('至少得启用一个"大语言模型"和一个"向量模型"才可用')
    # 获取模型数据
    model_data = get_platform_model()
    if model_data :
        model_df = pd.DataFrame(model_data)
        # 显示模型配置表格（可编辑是否启用）
        edited_model_df = st.data_editor(
            model_df.drop(columns=['id']),
            column_config={
                "是否启用": st.column_config.CheckboxColumn("是否启用"),
                "模型平台": st.column_config.Column(disabled=True),
                "模型类别": st.column_config.Column(disabled=True),
                "模型类型": st.column_config.Column(disabled=True),
                "模型Code": st.column_config.Column(disabled=True),
                "简介": st.column_config.Column(disabled=True),
                "更新时间": st.column_config.Column(disabled=True)
            },
            hide_index=True,
            use_container_width=True,
            key="model_config_editor"
        )

    # 模型配置保存按钮
    if st.button("保存模型配置修改", type="primary", key="save_model_config"):
        model_status_changes = []
        for (_, new_row), (_, old_row) in zip(edited_model_df.iterrows(), model_df.iterrows()):
            if new_row["是否启用"] != old_row["是否启用"]:
                model_status_changes.append({
                    "id": old_row["id"],
                    "model_name": old_row["模型名称"],
                    "is_set": new_row["是否启用"]
                })

        if not model_status_changes:
            st.warning("未检测到模型配置变更，无需保存")
            return

        with st.status("正在保存模型配置变更...", expanded=True) as status:
            success_count = 0
            total_changes = len(model_status_changes)

            for record in model_status_changes:
                try:
                    result = api.edit_platform_model(
                        record["id"],
                        record["is_set"]
                    )
                    if result.get('code') == 200:
                        success_count += 1
                    else:
                        st.error(f"模型状态更新失败（模型:{record['model_name']}）: {result.get('message', '未知错误')}")
                except Exception as e:
                    st.error(f"模型状态更新异常（模型:{record['model_name']}）: {str(e)}")

            status.update(
                label=f"模型配置保存完成！成功更新 {success_count}/{total_changes} 条记录",
                state="complete" if success_count == total_changes else "error"
            )

            if success_count > 0:
                st.toast(f"模型配置更新成功! 1s后自动刷新", icon="✅")
                st.cache_data.clear()
                time.sleep(1)
                st.rerun()

    # 添加新模型的表单
    with st.expander("添加新模型", expanded=False):
        with st.form(key="add_model_form"):
            col1, col2 = st.columns(2)
            with col1:
                platform_name = st.selectbox("模型平台*", ["阿里通义千问", "字节豆包"])
                model_type = st.selectbox("模型类别*", ["大语言模型", "向量模型"])
            with col2:
                model_code = st.text_input("模型Code*")
                model_name = st.text_input("模型名称*")
            description = st.text_area("模型简介")
            is_set = st.checkbox("是否启用", value=True)

            if st.form_submit_button("添加模型", type="primary"):
                if not all([platform_name, model_type, model_name]):
                    st.error("带*号的字段为必填项")
                else:
                    try:
                        # 转换模型类别为API需要的格式
                        api_model_type = "llm_models" if model_type == "大语言模型" else "embed_models"
                        result = api.add_platform_model(
                            platform_id=model_dict[platform_name],
                            is_set=int(is_set),
                            model_type=api_model_type,
                            model_name=model_name,
                            model_code=model_code,
                            description=description,
                        )

                        if result.get('code') == 200:
                            st.success("模型添加成功！")
                            st.cache_data.clear()
                            time.sleep(1)
                            st.rerun()
                        else:
                            st.error(f"添加失败: {result.get('message', '未知错误')}")
                    except Exception as e:
                        st.error(f"添加异常: {str(e)}")
