from __future__ import annotations

from fastapi import APIRouter, Body
from chatchat.server.db import db_handler
from chatchat.server.knowledge_base.kb_api import create_kb
from chatchat.server.knowledge_base.kb_service.base import KBServiceFactory
from chatchat.server.types.server.response.base import BaseResponse
from chatchat.utils import build_logger
from chatchat.server.utils import get_default_embedding
from chatchat.settings import Settings

logger = build_logger()

# 店铺相关接口
shop_router = APIRouter(prefix="/shop", tags=["shop"])


@shop_router.post("/get_shop_knowledge", summary="获取商店知识库关系", response_model=BaseResponse)
def get_shop_knowledge(
        shop_name: str = Body(..., description="商店名称"),
        knowledge_base: str = Body(..., description="知识库名称"),
        page: int = Body(1, gt=0, description="页码，从1开始"),
        per_page: int = Body(20, gt=0, lt=50, description="每页数量，范围1-50"),
        sort_direction: str = Body('desc', embed=True, description="排序类型"),
        sort_column: str = Body('add_time', embed=True, description="字段排序"),
):
    """
    获取商店知识库关系
    """

    result = db_handler.get_shop_knowledge(sort_direction, sort_column, page, per_page, shop_name, knowledge_base)
    if isinstance(result, str):
        return BaseResponse.error(f"更新失败，稍后再试,{result}")

    return BaseResponse.success(result)


@shop_router.post("/edit_shop_knowledge", summary="修改商店知识库关系", response_model=BaseResponse)
def edit_shop_knowledge(
        shop_knowledge_id: int = Body(0, description='商店知识库关系Id，如果为0则新建'),
        view_id: str = Body('', description="商店id"),
        shop_name: str = Body(..., min_length=1, description="商店名称,只能新增不能修改"),
        knowledge_base: str = Body(..., min_length=1, description="知识库名称"),
        description: str = Body(..., description="description"),
        prompt_name: str = Body(..., description="prompt"),
):
    """
    设置商店知识库关系
    """
    # 验证至少有一个存在
    if shop_knowledge_id == 0 and view_id == '':
        return BaseResponse.error("必须提供 view_id 或 shop_knowledge_id 中的一个")

    result = db_handler.edit_shop_knowledge(view_id, shop_knowledge_id, shop_name, knowledge_base,
                                            prompt_name, description)
    if result is None:
        return BaseResponse
    return BaseResponse.error(f"更新失败，稍后再试,{result}")


@shop_router.post("/add_shop_knowledge", summary="新增商店知识库关系", response_model=BaseResponse)
def add_shop_knowledge(
        shop_name: str = Body(..., min_length=1, description="商店名称"),
        view_id: str = Body(..., min_length=1, description="商店id"),
        knowledge_base: str = Body(..., min_length=1, description="知识库名称"),
        description: str = Body(..., description="description"),
        prompt_name: str = Body(..., description="prompt"),
):
    """
    设置商店知识库关系
    """

    result = db_handler.add_shop_knowledge(view_id, shop_name, knowledge_base, prompt_name, description)
    if result is None:
        return BaseResponse

    return BaseResponse.error(f"更新失败，稍后再试,{result}")


@shop_router.post("/del_shop_knowledge", summary="删除商店知识库关系", response_model=BaseResponse)
def del_shop_knowledge(
        shop_list_id: list[int] = Body(0, embed=True, description=''),
):
    """
    删除商店知识库关系
    """

    result = db_handler.del_shop_knowledge(shop_list_id)
    if result is None:
        return BaseResponse

    return BaseResponse.error(f"更新失败，稍后再试,{result}")


business_router = APIRouter(prefix="/business", tags=["shop"])

@business_router.post("/get_business_info", summary="获取商户信息", response_model=BaseResponse)
def get_business_info(
        user_name: str = Body(..., min_length=1, embed=True, description="商户账号"),
):
    """
    获取商户信息
    """

    result = db_handler.get_business_info(user_name)
    if isinstance(result, str):
        return BaseResponse.error(f"更新失败，稍后再试,{result}")

    return BaseResponse.success(result)


@business_router.post("/get_business_shop", summary="获取商户的商店信息", response_model=BaseResponse)
def get_business_shop(
        user_name: str = Body(..., min_length=1, description="商户账号"),
        shop_name: str = Body(None, embed=True, description="商店名称"),
):
    """
    获取商店知识库关系
    """

    result = db_handler.get_business_shop(user_name, shop_name)
    if isinstance(result, str):
        return BaseResponse.error(f"更新失败，稍后再试,{result}")

    return BaseResponse.success(result)


@business_router.post("/add_business_info", summary="新增商户信息", response_model=BaseResponse)
def get_business_shop(
        user_name: str = Body(..., min_length=1, description="商户账号"),
        business_name: str = Body(..., min_length=1, description="商店名称"),
):
    """
    新增商户信息
    """

    result = db_handler.add_business_info(user_name, business_name)
    if isinstance(result, str):
        return BaseResponse.error(f"更新失败，稍后再试,{result}")

    return BaseResponse.success(result)


@business_router.post("/edit_business_info", summary="编辑商户信息", response_model=BaseResponse)
def edit_business_info(
        business_id: int = Body(..., ge=0, description="商户名称"),
        business_name: str = Body(None, min_length=1, description="商户名称"),
        is_delete: int = Body(None, ge=0, description="商户名称"),
):
    """
    编辑商户信息
    """

    result = db_handler.edit_business_info(business_id, business_name, is_delete)
    if isinstance(result, str):
        return BaseResponse.error(f"更新失败，稍后再试,{result}")

    return BaseResponse.success(result)


@business_router.post("/add_shop", summary="新增商店", response_model=BaseResponse)
def add_shop(
        business_id: int = Body(..., ge=0, description="商户名称"),
        view_id: str = Body(..., min_length=1, description="商店窗口id"),
        shop_id: str = Body(..., min_length=1, description="商店id"),
        platform: str = Body(..., min_length=1, description="平台"),
        shop_name: str = Body(..., min_length=1, description="商店名称"),
        knowledge_base: str = Body(None, min_length=1, description="知识库名称"),
        prompt_name: str = Body(None, description="prompt"),
        description: str = Body(None, description="description"),
):
    """
    设置商店知识库关系
    """
    result = db_handler.add_shop(business_id, platform, view_id, shop_id, shop_name, knowledge_base, prompt_name,
                                 description)
    if isinstance(result, str):
        return BaseResponse.error(result)

    # 初次自动创建知识库
    name = f"{platform}_{shop_name}"

    # 是否存在
    kb = KBServiceFactory.get_service_by_alias(name)
    if kb is not None:
        return BaseResponse.success()

    res = create_kb(knowledge_base_name=name, kb_info=f"{platform} {shop_name}的知识库",
                    vector_store_type=Settings.kb_settings.DEFAULT_VS_TYPE,
                    embed_model=get_default_embedding())
    if res.code != 200:
        return res.msg
    return BaseResponse
