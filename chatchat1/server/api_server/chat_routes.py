from __future__ import annotations

import datetime
import os
from typing import Dict, List

from fastapi import APIRouter, Request, Body
from langchain.prompts.prompt import PromptTemplate
from sse_starlette import EventSourceResponse
from chatchat.server.types.server.response.base import BaseResponse

from chatchat.server.api_server.api_schemas import OpenAIChatInput
from chatchat.server.chat.chat import chat
from chatchat.server.chat.kb_chat import kb_chat
from chatchat.server.chat.feedback import chat_feedback
from chatchat.server.chat.file_chat import file_chat
from chatchat.server.db.repository import add_message_to_db
from chatchat.server.utils import (
    get_OpenAIClient,
    get_prompt_template,
    get_tool,
    get_tool_config,
)
from chatchat.settings import Settings
from chatchat.utils import build_logger
from .openai_routes import openai_request, OpenAIChatOutput
from chatchat.server.db import db_handler
from chatchat.server.db.db_handler import get_unknowledge_chat_history_by_id, set_knowledge_chat_history_by_id, \
    add_chat_history
from chatchat.server.knowledge_base.kb_doc_api import update_docs
from chatchat.server.knowledge_base.kb_service.base import KBServiceFactory
from chatchat.server.knowledge_base.utils import get_file_path, validate_kb_name

logger = build_logger()

chat_router = APIRouter(prefix="/chat", tags=["ChatChat 对话"])

# chat_router.post(
#     "/chat",
#     summary="与llm模型对话(通过LLMChain)",
# )(chat)

chat_router.post(
    "/feedback",
    summary="返回llm模型对话评分",
)(chat_feedback)

chat_router.post("/kb_chat", summary="知识库对话")(kb_chat)
chat_router.post("/file_chat", summary="文件对话")(file_chat)


@chat_router.post("/chat/completions", summary="兼容 openai 的统一 chat 接口")
async def chat_completions(
        request: Request,
        body: OpenAIChatInput,
) -> Dict:
    """
    请求参数与 openai.chat.completions.create 一致，可以通过 extra_body 传入额外参数
    tools 和 tool_choice 可以直接传工具名称，会根据项目里包含的 tools 进行转换
    通过不同的参数组合调用不同的 chat 功能：
    - tool_choice
        - extra_body 中包含 tool_input: 直接调用 tool_choice(tool_input)
        - extra_body 中不包含 tool_input: 通过 agent 调用 tool_choice
    - tools: agent 对话
    - 其它：LLM 对话
    以后还要考虑其它的组合（如文件对话）
    返回与 openai 兼容的 Dict
    """
    # import rich
    # rich.print(body)

    # 当调用本接口且 body 中没有传入 "max_tokens" 参数时, 默认使用配置中定义的值
    if body.max_tokens in [None, 0]:
        body.max_tokens = Settings.model_settings.MAX_TOKENS

    client = get_OpenAIClient(model_name=body.model, is_async=True)
    extra = {**body.model_extra} or {}
    for key in list(extra):
        delattr(body, key)

    # check tools & tool_choice in request body
    if isinstance(body.tool_choice, str):
        if t := get_tool(body.tool_choice):
            body.tool_choice = {"function": {"name": t.name}, "type": "function"}
    if isinstance(body.tools, list):
        for i in range(len(body.tools)):
            if isinstance(body.tools[i], str):
                if t := get_tool(body.tools[i]):
                    body.tools[i] = {
                        "type": "function",
                        "function": {
                            "name": t.name,
                            "description": t.description,
                            "parameters": t.args,
                        },
                    }

    conversation_id = extra.get("conversation_id")

    # chat based on result from one choiced tool
    if body.tool_choice:
        tool = get_tool(body.tool_choice["function"]["name"])
        if not body.tools:
            body.tools = [
                {
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.args,
                    },
                }
            ]
        if tool_input := extra.get("tool_input"):
            try:
                message_id = (
                    add_message_to_db(
                        chat_type="tool_call",
                        query=body.messages[-1]["content"],
                        conversation_id=conversation_id,
                    )
                    if conversation_id
                    else None
                )
            except Exception as e:
                logger.warning(f"failed to add message to db: {e}")
                message_id = None

            tool_result = await tool.ainvoke(tool_input)
            prompt_template = PromptTemplate.from_template(
                get_prompt_template("rag", "default"), template_format="jinja2"
            )
            body.messages[-1]["content"] = prompt_template.format(
                context=tool_result, question=body.messages[-1]["content"]
            )
            del body.tools
            del body.tool_choice
            extra_json = {
                "message_id": message_id,
                "status": None,
                "model": body.model,
            }
            header = [
                {
                    **extra_json,
                    "content": f"{tool_result}",
                    "tool_call": tool.get_name(),
                    "tool_output": tool_result.data,
                    "is_ref": False if tool.return_direct else True,
                }
            ]
            if tool.return_direct:
                def temp_gen():
                    yield OpenAIChatOutput(**header[0]).model_dump_json()
                return EventSourceResponse(temp_gen())
            else:
                return await openai_request(
                    client.chat.completions.create,
                    body,
                    extra_json=extra_json,
                    header=header,
                )

    # agent chat with tool calls
    if body.tools:
        try:
            message_id = (
                add_message_to_db(
                    chat_type="agent_chat",
                    query=body.messages[-1]["content"],
                    conversation_id=conversation_id,
                )
                if conversation_id
                else None
            )
        except Exception as e:
            logger.warning(f"failed to add message to db: {e}")
            message_id = None

        chat_model_config = {}  # TODO: 前端支持配置模型
        tool_names = [x["function"]["name"] for x in body.tools]
        tool_config = {name: get_tool_config(name) for name in tool_names}
        result = await chat(
            query=body.messages[-1]["content"],
            metadata=extra.get("metadata", {}),
            conversation_id=extra.get("conversation_id", ""),
            message_id=message_id,
            history_len=-1,
            history=body.messages[:-1],
            stream=body.stream,
            chat_model_config=extra.get("chat_model_config", chat_model_config),
            tool_config=extra.get("tool_config", tool_config),
            max_tokens=body.max_tokens,
        )
        return result
    else:  # LLM chat directly
        try:  # query is complex object that unable add to db when using qwen-vl-chat
            message_id = (
                add_message_to_db(
                    chat_type="llm_chat",
                    query=body.messages[-1]["content"],
                    conversation_id=conversation_id,
                )
                if conversation_id
                else None
            )
        except Exception as e:
            logger.warning(f"failed to add message to db: {e}")
            message_id = None

        extra_json = {
            "message_id": message_id,
            "status": None,
        }
        return await openai_request(
            client.chat.completions.create, body, extra_json=extra_json
        )


@chat_router.post("/history", summary="获取聊天记录", response_model=BaseResponse)
def get_chat_history(
        page: int = Body(1, gt=0, description="页码，从1开始"),
        per_page: int = Body(20, gt=0, lt=50, description="每页数量，范围1-50"),
        sort_direction: str = Body('desc',embed=True, description="排序类型"),
        sort_column: str = Body('add_time',embed=True, description="字段排序"),
) -> BaseResponse:
    """
    获取分页聊天记录
    Args:
        page: 当前页码
        per_page: 每页记录数
        sort_direction: 排序类型
        sort_column: 字段排序

    Returns:
        BaseResponse: 包含聊天记录列表或错误信息
    """
    result = db_handler.get_chat_history(sort_direction,sort_column,page, per_page)
    if isinstance(result, str):  # 明确判断错误返回
        return BaseResponse.error(f"获取聊天记录失败: {result}")
    return BaseResponse.success(data=result)


@chat_router.post("/history_2_knowledge", summary="聊天记录生成知识库", response_model=BaseResponse)
def chat_history_2_knowledge(
        id_list: List[int] = Body([], description="需要转换的聊天记录ID列表"),
        knowledge_base: str = Body(..., description="目标知识库名称"),
) -> BaseResponse:
    """
    将聊天记录转换为知识库文档

    Args:
        id_list: 聊天记录ID列表
        knowledge_base: 目标知识库名称

    Returns:
        BaseResponse: 操作结果
    """
    # 参数校验
    if not id_list:
        return BaseResponse(code=403, msg="请选择要转换的聊天记录")

    history_list = get_unknowledge_chat_history_by_id(id_list)
    if not history_list:
        return BaseResponse(code=403, msg="未找到符合条件的聊天记录")

    # 知识库校验
    if not validate_kb_name(knowledge_base):
        return BaseResponse(code=403, msg="知识库名称不合法")

    kb_service = KBServiceFactory.get_service_by_name(knowledge_base)
    if kb_service is None:
        return BaseResponse(code=404, msg=f"知识库 {knowledge_base} 不存在")

    # 准备文件名和路径
    max_id = max(id_list)
    timestamp = datetime.datetime.now().strftime("%Y%m%d")
    file_name = f"history_id_{max_id}_{timestamp}.txt"
    file_path = get_file_path(knowledge_base_name=knowledge_base, doc_name=file_name)

    def delete_file():
        # 存在且报错则删除
        if os.stat(file_path):
            os.remove(file_path)

    try:
        # 写入文件
        with open(file_path, "w", encoding="utf-8") as f:
            for record in history_list:
                f.write(f"问题：{record['query']}\n答案：{record['answer']}\n\n")

        # 更新知识库
        result = update_docs(
            knowledge_base_name=knowledge_base,
            file_names=[file_name],
            chunk_size=Settings.kb_settings.CHUNK_SIZE,  # 调整聊天问题对应的值
            chunk_overlap=Settings.kb_settings.OVERLAP_SIZE,
            zh_title_enhance=True,
            override_custom_docs=False,
            not_refresh_vs_cache=False,
            docs=''
        )

        if result.code != 200:
            delete_file()
            return BaseResponse.error(f"知识库更新失败: {result.msg}")

        # 标记已转换记录
        if set_knowledge_chat_history_by_id(id_list) is None:
            # 返回文件名
            return BaseResponse.success(file_name)
        return BaseResponse.error("记录状态更新失败")

    except Exception as e:
        delete_file()
        return BaseResponse.error(f"转换过程中发生错误: {str(e)}")


@chat_router.post("/manually_add_query", summary="手动添加问题记录", response_model=BaseResponse)
def chat_history_2_knowledge(
        query: str = Body(..., min_length=1, description="问题"),
        answer: str = Body(..., min_length=1, description="答案"),
        knowledge_base: str = Body(..., min_length=1, description="知识库"),
) -> BaseResponse:
    """
        手动添加问题记录
    """

    result = add_chat_history('user', query, '', knowledge_base, '', '', '', answer, '', '')
    if isinstance(result, str):  # 明确判断错误返回
        return BaseResponse.error(f"手动添加聊天记录失败: {result}")
    return BaseResponse.success(data=result)


@chat_router.post("/edit_chat_history", summary="修改问题记录", response_model=BaseResponse)
def edit_chat_history(
        query: str = Body(..., min_length=1, description="问题"),
        answer: str = Body(..., min_length=1, description="答案"),
        id: int = Body(..., gt=0, description="知识库"),
) -> BaseResponse:
    """
        修改问题记录
    """

    result = db_handler.edit_chat_history(id, query, answer)
    if isinstance(result, str):  # 明确判断错误返回
        return BaseResponse.error(f"修改问题记录失败: {result}")
    return BaseResponse.success(data=result)
