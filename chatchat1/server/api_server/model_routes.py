from __future__ import annotations

from fastapi import APIRouter, Body, HTTPException, status
from pydantic import BaseModel, Field
from chatchat.server.db import db_handler
from chatchat.server.types.server.response.base import BaseResponse
from chatchat.utils import build_logger

logger = build_logger()

# 模型相关接口
model_router = APIRouter(prefix="/model", tags=["model"])


@model_router.post("/set_platform_api_key", summary="配置模型API Key", response_model=BaseResponse)
def set_platform_api_key(
        api_key: str = Body(..., embed=True, description="API key"),
        platform_id: int = Body(..., gt=0, description="平台 ID"),
        platform_type: str = Body(..., min_length=1, description="平台类型"),
) -> BaseResponse:
    """
    设置模型API Key
    - 成功时返回空数据
    - 失败时返回错误信息
    """
    result = db_handler.set_platform_api_key(api_key, platform_id, platform_type)
    if result is None:  # 假设成功时返回False/None，失败时返回错误信息
        return BaseResponse.success()
    return BaseResponse.error(f"更新失败: {result}")


@model_router.post("/get_platform_info", summary="获取大模型平台配置信息", response_model=BaseResponse)
def get_platform_info() -> BaseResponse:
    """
    获取模型信息（自动脱敏API Key）
    - 始终返回BaseResponse结构
    - API Key显示前8位
    """
    result = db_handler.get_platform_info()
    if isinstance(result, str):  # 明确判断类型
        return BaseResponse.error(f"获取失败: {result}")

    def mask_api_key(api_key: str, visible_chars: int = 8) -> str:
        """脱敏处理API Key，默认显示前8位"""
        if len(api_key) <= visible_chars:
            return api_key
        return api_key[:visible_chars] + "*" * (len(api_key) - visible_chars)

    if isinstance(result, list):
        for item in result:
            if isinstance(item, dict) and "api_key" in item:
                item["api_key"] = mask_api_key(item["api_key"])

    return BaseResponse.success(data=result)


@model_router.post("/get_platform_model", summary="获取大模型平台的模型", response_model=BaseResponse)
def get_platform_model(
        is_show_all: bool = Body(False, embed=True, description="默认显示已设置的"),
        platform_type: str = Body('', embed=True, description="模板名字"),
        model_type: str = Body('', embed=True, description="模型类型"),
) -> BaseResponse:
    result = db_handler.get_platform_model(platform_type, model_type, is_show_all)
    if isinstance(result, str):  # 明确判断类型
        return BaseResponse.error(f"获取失败: {result}")
    return BaseResponse.success(result)


@model_router.post("/edit_platform_model", summary="编辑大模型平台的模型", response_model=BaseResponse)
def edit_platform_model(
        model_id: int = Body(..., gt=0, description="主键id"),
        is_set: int = Body(0, description="开关"),
) -> BaseResponse:
    result = db_handler.edit_platform_model(model_id, is_set)
    if isinstance(result, str):  # 明确判断类型
        return BaseResponse.error(f"操作失败: {result}")
    return BaseResponse.success(result)


@model_router.post("/add_platform_model", summary="添加大模型平台的模型", response_model=BaseResponse)
def add_platform_model(
        platform_id: int = Body(..., gt=0, description=""),
        is_set: int = Body(0, embed=True, description="开关"),
        model_type: str = Body(..., min_length=1, description=""),
        model_name: str = Body(..., min_length=1, description=""),
        model_code: str = Body(..., min_length=1, description=""),
        description: str = Body(..., description=""),

) -> BaseResponse:
    result = db_handler.add_platform_model(platform_id, model_type, model_name, model_code, description, is_set)
    if isinstance(result, str):  # 明确判断类型
        return BaseResponse.error(f"操作失败: {result}")
    return BaseResponse.success(result)
