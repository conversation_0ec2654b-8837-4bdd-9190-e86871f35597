from fastapi import APIRouter, Body
from chatchat.server.db import db_handler
from chatchat.server.types.server.response.base import BaseResponse
from chatchat.server.utils import get_server_configs, get_prompt_template_key
from chatchat.server import utils

server_router = APIRouter(prefix="/server", tags=["Server State"])

available_template_types = get_prompt_template_key()  # 改为读表 list(Settings.prompt_settings.model_fields.keys())

# 服务器相关接口
server_router.post(
    "/configs",
    summary="获取服务器原始配置信息",
)(get_server_configs)


@server_router.post("/get_prompt_template", summary="获取服务区配置的 prompt 模板", response_model=BaseResponse)
def get_server_prompt_template(
        type: str = Body(
            "llm_model", description="模板类型，可选值：{available_template_types}"
        ),
        name: str = Body("default", description="模板名称"),
):
    prompt_template = utils.get_prompt_template(type=type, name=name)
    if prompt_template is None:
        return BaseResponse.error("Prompt template not found")
    return BaseResponse.success(prompt_template)


# api key模板获取
@server_router.post("/get_prompt_template_list", summary="获取模板列表", response_model=BaseResponse)
def get_prompt_template(
        prompt_type: str = Body(..., description="模板类型"),
        prompt_name: str = Body(..., description="模板名字"),
        page: int = Body(1, gt=0, description="页码，从1开始"),
        per_page: int = Body(20, gt=0, lt=50, description="每页数量，范围1-50"),
        sort_direction: str = Body('desc',embed=True, description="排序类型"),
        sort_column: str = Body('add_time', embed=True, description="字段排序"),
):
    """
    获取模板列表

    """

    result = db_handler.get_prompt_template_list(sort_direction,sort_column,prompt_type, prompt_name, page, per_page)
    if isinstance(result, str):
        return BaseResponse.error(f"更新失败，稍后再试,{result}")

    return BaseResponse.success(result)


@server_router.post("/edit_prompt_template", summary="修改模板", response_model=BaseResponse)
def edit_prompt_template(
        prompt_id: int = Body(..., description="模板类型id"),
        prompt_type: str = Body(..., min_length=1, description="模板类型"),
        prompt_name: str = Body(..., min_length=1, description="模板名字"),
        template: str = Body(..., min_length=1, description="template"),
        description: str = Body(..., description="description"),
):
    """
    编辑模板

    """
    result = db_handler.edit_prompt_template(prompt_id, prompt_type, prompt_name, template, description)
    if result is None:
        return BaseResponse.success()

    return BaseResponse.error(f"更新失败，稍后再试,{result}")


@server_router.post("/add_prompt_template", summary="添加模板", response_model=BaseResponse)
def add_prompt_template(
        prompt_type: str = Body(..., min_length=1, description="模板类型"),
        prompt_name: str = Body(..., min_length=1, description="模板名字"),
        template: str = Body(..., min_length=1, description="template"),
        description: str = Body(..., description="description"),
):
    """
    添加模板

    """
    result = db_handler.add_prompt_template(prompt_type, prompt_name, template, description)
    if result is None:
        return BaseResponse.success()

    return BaseResponse.error(f"更新失败，稍后再试,{result}")


# api key模板修改
@server_router.post("/del_prompt_template", summary="删除模板", response_model=BaseResponse)
def del_prompt_template(
        prompt_list_id: list[int] = Body(...,embed=True, description="模板类型id"),
):
    """
    删除模板

    """
    try:
        result = db_handler.del_prompt_template(prompt_list_id)
        if result is None:
            return BaseResponse.success()

        return BaseResponse.error(f"删除失败: {result}")
    except Exception as e:
        return BaseResponse.error(f"服务器错误: {str(e)}")
