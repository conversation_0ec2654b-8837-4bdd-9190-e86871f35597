from __future__ import annotations

import time
import requests
from fastapi import APIRouter, Body, Form
from fastapi import Request
from starlette.responses import JSONResponse

from chatchat.server.types.server.response.base import BaseResponse
from chatchat.settings import Settings
from chatchat.utils import build_logger

logger = build_logger()

# 店铺相关接口
user_router = APIRouter(prefix="/api/auto", tags=["用户"])
_user_login_session = 'identity'


@user_router.post("/login", summary="用户登录", response_model=BaseResponse)
def login(request: Request, username: str = Form(...), password: str = Form(...)):
    """
    登录
    """
    url = Settings.basic_settings.MIDDLE_PLATFORM_HOST + request.url.path
    data = {
        'username': username,
        'password': password,
    }

    response = requests.post(url, data=data)
    res = response.json()
    session_time = '0'
    if res['code'] == 200:
        # 成功则有效期2周
        session_time = str(int(time.time().__add__(14 * 24 * 3600)))

    # 临时创建文件记录有效期
    file_name = _user_login_session
    # 一周时间
    with open(file_name, "w+") as w:
        w.write(session_time)

    return res


@user_router.post("/register", summary="用户登录", response_model=BaseResponse)
def register(request: Request, username: str = Form(...), password: str = Form(...), email: str = Form(...),
             company: str = Form(...),phone: str = Form(...)):
    """
    注册
    """
    url = Settings.basic_settings.MIDDLE_PLATFORM_HOST + request.url.path
    data = {
        'username': username,
        'password': password,
        'email': email,
        'phone': phone,
        'company': company,
    }

    response = requests.post(url, data=data)
    return response.json()


@user_router.post("/change_password")
def change_password(request: Request, password: str = Form(...), newPassword: str = Form(...)):
    """
    修改密码
    """
    url = Settings.basic_settings.MIDDLE_PLATFORM_HOST + request.url.path
    data = {
        'password': password,
        'newPassword': newPassword,
    }
    header = {
        'token': request.headers.get('token')
    }
    response = requests.post(url, data=data, headers=header)
    return response.json()


@user_router.post("/reset_password")
def reset_password(request: Request, username: str = Form(...), password: str = Form(...), email: str = Form(...),
                   phone: str = Form(...)):
    """
    重置密码
    """

    url = Settings.basic_settings.MIDDLE_PLATFORM_HOST + request.url.path
    data = {
        'username': username,
        'password': password,
        'email': email,
        'phone': phone,
    }
    response = requests.post(url, data=data)
    return response.json()


@user_router.post("/logout")
def logout():
    """
    退出
    """
    session_time = '0'
    with open(_user_login_session, "w+") as w:
        w.write(session_time)

    return JSONResponse(
        content = {"msg": "退出成功", "data": "", "code": 200})
