from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Integer, String, Float
from chatchat.server.db.base import Base


class ProductChoicesModel(Base):
    """
    产品规格信息模型
    """

    __tablename__ = "product_choices"
    id = Column(Integer, primary_key=True, autoincrement=True, comment="id")
    product_info_id = Column(Integer, comment="id")
    product_id = Column(String, comment="id")
    standard  = Column(String, comment="")
    description  = Column(String, comment="")
    amount  = Column(Integer, comment="")
    single_price  = Column(Float, comment="")
    group_price  = Column(Float, comment="")
    reference_price  = Column(Float, comment="")
    image  = Column(String, comment="")
    is_delete = Column(Integer,default=0, comment="")
    add_time = Column(Integer, comment="")
    update_time = Column(Integer, comment="")

    def __repr__(self):
        return (f"<product_choices('{self.id}', '{self.standard}', '{self.description}', '{self.amount}', '{self.single_price}', '{self.group_price}', '{self.reference_price}', '{self.image}', '{self.is_delete}', '{self.add_time}', '{self.update_time}')>")