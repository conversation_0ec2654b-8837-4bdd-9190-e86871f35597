from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Integer, String, func ,UniqueConstraint
from chatchat.server.db.base import Base


class ShopInfoModel(Base):
    """
    聊天记录模型
    """
    __table_args__ = (
        UniqueConstraint('platform', 'platform_shop_id', name='uix_platform_shop'),
        # 可以添加其他需要的唯一约束
    )
    __tablename__ = "shop_info"
    id = Column(Integer, primary_key=True, autoincrement=True, comment="id")
    business_id = Column(String, comment="")
    platform = Column(String, comment="")
    view_id = Column(String, comment="")
    platform_shop_id = Column(String, comment="")
    shop_name = Column(String, comment="")
    knowledge_base = Column(String, comment="")
    prompt_name = Column(String, comment="")
    description = Column(String, comment="")
    add_time = Column(Integer, comment="")
    update_time = Column(Integer, comment="")
    collectionState = Column(Integer, comment="")

    def __repr__(self):
        return f"<shop_info(id='{self.id}',business_id = '{self.business_id}',platform_shop_id = '{self.platform_shop_id}',platform = '{self.platform}', view_id = '{self.view_id}', shop_name = '{self.shop_name}', knowledge_base = '{self.knowledge_base}', prompt_name = '{self.prompt_name}', description = '{self.description}', add_time = '{self.add_time}', update_time = '{self.update_time}', collectionState = '{self.collectionState}')>"
