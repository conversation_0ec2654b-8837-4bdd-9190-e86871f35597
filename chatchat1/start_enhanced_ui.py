#!/usr/bin/env python3
"""
ChatChat AI - 增强版UI启动脚本
科技感高交互设计界面启动器

使用方法:
python chatchat/start_enhanced_ui.py

或者直接运行:
python -m chatchat.start_enhanced_ui
"""

import os
import sys
import subprocess
from pathlib import Path

def get_project_root():
    """获取项目根目录"""
    current_file = Path(__file__).resolve()
    # 向上查找直到找到包含chatchat目录的根目录
    for parent in current_file.parents:
        if (parent / "chatchat").exists():
            return parent
    return current_file.parent.parent

def check_dependencies():
    """检查必要的依赖"""
    required_packages = [
        'streamlit',
        'streamlit-antd-components',
        'streamlit-chatbox',
        'streamlit-extras'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def start_enhanced_ui():
    """启动增强版UI"""
    print("🚀 ChatChat AI - 增强版UI启动器")
    print("=" * 50)
    
    # 检查依赖
    print("🔍 检查依赖包...")
    if not check_dependencies():
        sys.exit(1)
    print("✅ 依赖检查通过")
    
    # 获取项目根目录
    project_root = get_project_root()
    webui_path = project_root / "chatchat" / "webui.py"
    
    if not webui_path.exists():
        print(f"❌ 找不到webui.py文件: {webui_path}")
        sys.exit(1)
    
    print(f"📁 项目根目录: {project_root}")
    print(f"🌐 WebUI文件: {webui_path}")
    
    # 设置环境变量
    os.environ["STREAMLIT_THEME_BASE"] = "light"
    os.environ["STREAMLIT_THEME_PRIMARY_COLOR"] = "#667eea"
    os.environ["STREAMLIT_THEME_BACKGROUND_COLOR"] = "#ffffff"
    os.environ["STREAMLIT_THEME_SECONDARY_BACKGROUND_COLOR"] = "#f8fafc"
    os.environ["STREAMLIT_THEME_TEXT_COLOR"] = "#1e293b"
    
    # 构建启动命令
    cmd = [
        sys.executable, "-m", "streamlit", "run", str(webui_path),
        "--server.enableCORS=true",
        "--server.enableXsrfProtection=false",
        "--global.developmentMode=false",
        "--client.toolbarMode=minimal",
        "--theme.base=light",
        "--theme.primaryColor=#667eea",
        "--theme.backgroundColor=#ffffff",
        "--theme.secondaryBackgroundColor=#f8fafc",
        "--theme.textColor=#1e293b",
        "--server.headless=true",
        "--browser.gatherUsageStats=false"
    ]
    
    print("🎨 启动增强版UI界面...")
    print("💡 特色功能:")
    print("   • 科技感浅色主题设计")
    print("   • Neumorphism视觉效果")
    print("   • 现代化交互体验")
    print("   • 智能配置面板")
    print("   • 增强型聊天界面")
    print()
    print("🌐 启动完成后，请在浏览器中访问显示的地址")
    print("=" * 50)
    
    try:
        # 切换到项目根目录
        os.chdir(project_root)
        
        # 启动streamlit
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在退出...")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        sys.exit(1)

def main():
    """主函数"""
    try:
        start_enhanced_ui()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
