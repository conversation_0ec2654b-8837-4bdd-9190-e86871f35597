import sys

import streamlit as st
import streamlit_antd_components as sac

from chatchat import __version__
from chatchat.server.utils import api_address
from chatchat.webui_pages.chat_history import chat_history
from chatchat.webui_pages.kb_chat import kb_chat
from chatchat.webui_pages.knowledge_base.knowledge_base import knowledge_base_page
from chatchat.webui_pages.prompt_setting import prompt_setting
from chatchat.webui_pages.shop_setting import shop_setting
from chatchat.webui_pages.system_seting import system_setting
from chatchat.webui_pages.utils import *

api = ApiRequest(base_url=api_address())

if __name__ == "__main__":
    is_lite = "lite" in sys.argv  # TODO: remove lite mode

    # 设置页面配置
    st.set_page_config(
        page_title="深维智眸 - 智能客服系统",
        page_icon="🤖",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # 科技感高交互设计样式 - 浅色简约风格
    st.markdown(
        """
        <style>
        /* 导入现代字体 */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap');

        /* 隐藏Streamlit默认元素 */
        #MainMenu {visibility: hidden !important;}
        .stDeployButton {display: none !important;}
        footer {visibility: hidden !important;}
        .stApp > header {visibility: hidden !important;}
        .viewerBadge_container__1QSob {display: none !important;}
        .stActionButton {display: none !important;}

        /* 精确隐藏header相关元素 */
        .stAppHeader {display: none !important;}
        .stAppToolbar {display: none !important;}
        .stMainMenu {display: none !important;}
        .stAppDeployButton {display: none !important;}
        .stToolbarActions {display: none !important;}
        [data-testid="stHeader"] {display: none !important;}
        [data-testid="stToolbar"] {display: none !important;}
        [data-testid="stMainMenu"] {display: none !important;}
        [data-testid="stAppDeployButton"] {display: none !important;}
        [data-testid="stToolbarActions"] {display: none !important;}

        /* 隐藏所有header相关的CSS类 */
        /* .st-emotion-cache-1ffuo7c {display: none !important;}
        .st-emotion-cache-14vh5up {display: none !important;}
        .st-emotion-cache-1j22a0y {display: none !important;}
        .st-emotion-cache-70qvj9 {display: none !important;}
        .st-emotion-cache-scp8yw {display: none !important;}
        .st-emotion-cache-1p1m4ay {display: none !important;}
        .st-emotion-cache-czk5ss {display: none !important;} */



        /* 全局样式重置 - 浅色高级简约风格 */
        .stApp {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #cbd5e1 50%, #f1f5f9 75%, #ffffff 100%);
            font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, sans-serif;
            color: #1e293b;
            min-height: 100vh;
        }

        /* 主容器样式 - 移除白色容器 */
        .block-container {
            padding-top: 1rem;
            padding-bottom: 1rem;
            max-width: 100%;
            background: transparent !important;
        }

        /* 移除主内容区域的白色背景 */
        .main .block-container {
            background: transparent !important;
            box-shadow: none !important;
            border: none !important;
        }

        /* 侧边栏样式 - 科技感浅色 */
        .stSidebar {
            background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
            border-right: 1px solid #e2e8f0;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.05);
        }

        [data-testid="stSidebarUserContent"] {
            padding-top: 1rem;
            background: transparent;
        }

        /* 侧边栏标题样式 */
        .sidebar-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
            text-align: center;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 菜单项样式增强 */
        .ant-menu {
            background: transparent !important;
            border: none !important;
        }

        .ant-menu-item {
            background: rgba(255, 255, 255, 0.7) !important;
            border-radius: 12px !important;
            margin: 8px 0 !important;
            border: 1px solid rgba(226, 232, 240, 0.8) !important;
            backdrop-filter: blur(10px) !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
        }

        .ant-menu-item:hover {
            background: rgba(102, 126, 234, 0.1) !important;
            border-color: #667eea !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15) !important;
        }

        .ant-menu-item-selected {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            border-color: transparent !important;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
        }

        /* 侧边栏收缩/展开按钮样式 */
        [data-testid="collapsedControl"] {
            display: block !important;
            position: fixed !important;
            top: 1rem !important;
            left: 1rem !important;
            z-index: 999999 !important;
            background: rgba(255, 255, 255, 0.95) !important;
            border-radius: 50% !important;
            width: 48px !important;
            height: 48px !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
            backdrop-filter: blur(15px) !important;
            border: 1px solid rgba(226, 232, 240, 0.8) !important;
        }

        /* 收缩按钮内部样式 */
        [data-testid="collapsedControl"] button {
            width: 100% !important;
            height: 100% !important;
            border-radius: 50% !important;
            border: none !important;
            background: transparent !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            color: #667eea !important;
            font-size: 1.2rem !important;
        }

        /* 收缩按钮悬停效果 */
        [data-testid="collapsedControl"]:hover {
            background: rgba(102, 126, 234, 0.1) !important;
            transform: scale(1.1) !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.25) !important;
        }

        /* 确保侧边栏收缩按钮始终可见（优先级最高） */
        [data-testid="collapsedControl"] {
            display: block !important;
        }
        [data-testid="stSidebarCollapseButton"] {
            position: fixed !important;
            left: 255px;
            z-index: 999999 !important;
            display: block;
        }

        /* 主内容区域样式 - 简化版 */
        .main .block-container {
            padding-top: 1rem;
            padding-bottom: 1rem;
        }

        /* 移除默认的容器边距 */
        .element-container {
            margin-bottom: 0.5rem;
        }

        /* 卡片样式 */
        .custom-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 1.5rem;
            margin: 1rem 0;
            border: 1px solid rgba(226, 232, 240, 0.8);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .custom-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
        }

        /* 按钮样式增强 */
        .stButton > button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .stButton > button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }

        /* 输入框样式 */
        .stTextInput > div > div > input,
        .stSelectbox > div > div > select,
        .stTextArea > div > div > textarea {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 12px;
            padding: 0.75rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .stTextInput > div > div > input:focus,
        .stSelectbox > div > div > select:focus,
        .stTextArea > div > div > textarea:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* 数据表格样式 */
        .stDataFrame {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid rgba(226, 232, 240, 0.8);
            backdrop-filter: blur(10px);
        }

        /* 指标卡片样式 */
        .metric-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(226, 232, 240, 0.8);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
        }

        /* 底部容器样式 */
        [data-testid="stBottomBlockContainer"] {
            padding-bottom: 2rem;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(226, 232, 240, 0.3);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .block-container {
                padding: 1rem;
            }

            .main-content {
                margin: 0.5rem;
                padding: 1rem;
            }
        }
        </style>
        """,
        unsafe_allow_html=True,
    )

    # 侧边栏导航
    with st.sidebar:
        # 系统标题和版本信息
        st.markdown(
            f"""
            <div class="sidebar-title">
                🤖 深维智眸
            </div>
            <div style="text-align: center; color: #64748b; font-size: 0.9rem; margin-bottom: 2rem;">
                智能客服系统 v{__version__}
            </div>
            """,
            unsafe_allow_html=True
        )

        # 导航菜单
        selected_page = sac.menu(
            [
                sac.MenuItem("智能对话", icon="chat-dots", description="AI智能客服对话"),
                sac.MenuItem("知识库管理", icon="database-gear", description="管理知识库内容"),
                sac.MenuItem("聊天记录", icon="chat-left-text", description="查看历史对话"),
                sac.MenuItem("客服模板", icon="clipboard-data", description="管理客服模板"),
                sac.MenuItem("商店配置", icon="shop", description="商店相关设置"),
                sac.MenuItem("系统设置", icon="gear", description="系统参数配置"),
            ],
            key="selected_page",
            open_index=0,
            size="large",
        )

        # 分割线
        st.markdown(
            """
            <div style="margin: 2rem 0; height: 1px; background: linear-gradient(90deg, transparent, #e2e8f0, transparent);"></div>
            """,
            unsafe_allow_html=True
        )

        # 系统状态指示器
        st.markdown(
            """
            <div class="custom-card">
                <h4 style="margin: 0 0 1rem 0; color: #1e293b;">系统状态</h4>
                <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                    <div style="width: 8px; height: 8px; background: #10b981; border-radius: 50%; margin-right: 8px;"></div>
                    <span style="color: #374151; font-size: 0.9rem;">API服务正常</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 8px; height: 8px; background: #10b981; border-radius: 50%; margin-right: 8px;"></div>
                    <span style="color: #374151; font-size: 0.9rem;">数据库连接正常</span>
                </div>
            </div>
            """,
            unsafe_allow_html=True
        )

    # 主内容区域 - 移除白色方框，使用更简洁的布局
    # 根据选择的页面显示内容
    if selected_page == "知识库管理":
        knowledge_base_page(api=api, is_lite=is_lite)
    elif selected_page == "智能对话":
        kb_chat(api=api)
    elif selected_page == "聊天记录":
        chat_history(api=api)
    elif selected_page == "系统设置":
        system_setting(api=api)
    elif selected_page == "客服模板":
        prompt_setting(api=api)
    elif selected_page == "商店配置":
        shop_setting(api=api)
    else:
        kb_chat(api=api)  # 默认页面
