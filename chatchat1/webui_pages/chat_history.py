import datetime
import time

import pandas as pd
import streamlit as st
from chatchat.webui_pages.utils import ApiRequest


def chat_history(api: ApiRequest):
    st.subheader("聊天记录")
    default_per_page = 10

    # Initialize session state for page and sorting
    if 'chat_history_page' not in st.session_state:
        st.session_state.chat_history_page = 1
    if 'chat_sort_column' not in st.session_state:
        st.session_state.chat_sort_column = "add_time"  # 默认按创建时间排序
    if 'chat_sort_direction' not in st.session_state:
        st.session_state.chat_sort_direction = "desc"  # 默认降序

    # Initialize edited data
    if 'edited_data' not in st.session_state:
        st.session_state.edited_data = {}

    # Combined function to get data and total count with debounce

    def get_data(page, per_page, sort_column, sort_direction):
        res = api.get_chat_history(sort_direction, sort_column, page, per_page)
        value = []
        for v in res['chat_history']:
            value.append({
                "选择": False,  # 添加选择列
                "ID": v.get('id', ''),
                "店铺": v.get('shop_name') if v.get('shop_name') is not None else '',
                "已转换": '✅' if v['is_update_knowledge'] == 'true' else '❌',
                "知识库": v.get('knowledge_base', '未指定'),
                "客服模板": v.get('prompt_name'),
                "问题": v.get('query', ''),
                "答案": v.get('answer', ''),
                "添加类型": '系统' if v.get('add_type', 'system') == 'system' else '用户',
                "创建时间": datetime.datetime.fromtimestamp(v['add_time']).strftime("%Y-%m-%d %H:%M:%S") if v.get(
                    'add_time') else '未知时间',
                "add_time": v.get('add_time', 0),  # 用于排序的原始时间戳
            })
        return value, res.get('total', 0)

    def update_sort_and_rerun():
        """更新排序参数并重新加载页面"""
        # 获取当前选择框的值
        new_sort_column = st.session_state.chat_sort_column_select
        new_sort_direction = st.session_state.chat_sort_direction_select

        # 只有当排序条件真正改变时才更新
        if (new_sort_column != st.session_state.chat_sort_column or
                new_sort_direction != st.session_state.chat_sort_direction):
            st.session_state.chat_sort_column = new_sort_column
            st.session_state.chat_sort_direction = new_sort_direction
            st.session_state.chat_history_page = 1  # 排序后重置到第一页
            # st.rerun()

    # Get initial data and total count with sorting
    data, total_count = get_data(
        st.session_state.chat_history_page,
        default_per_page,
        st.session_state.chat_sort_column,
        st.session_state.chat_sort_direction
    )
    df = pd.DataFrame(data)

    # Display data table with editing capabilities
    edited_df = st.data_editor(
        df,
        column_config={
            "选择": st.column_config.CheckboxColumn("选择", default=False),
            "问题": st.column_config.TextColumn("问题"),
            "答案": st.column_config.TextColumn("答案", width="large"),
            "add_time": None,  # 隐藏用于排序的原始时间戳列
        },
        hide_index=True,
        column_order=["选择", "店铺", "已转换", '添加类型', "问题", "答案", "知识库", "客服模板", "创建时间"],
        use_container_width=True,
        key=f"data_editor_{st.session_state.chat_history_page}",
        num_rows="fixed"  # 防止添加新行
    )

    # 检查是否有编辑并保存
    if not edited_df.equals(df):
        # 找出被修改的行
        changed_rows = edited_df[~edited_df.apply(tuple, 1).isin(df.apply(tuple, 1))]

        for _, row in changed_rows.iterrows():
            record_id = row['ID']
            # 只保存问题或答案有变化的记录
            original_row = df[df['ID'] == record_id].iloc[0]
            changes = False

            if row['问题'] != original_row['问题'] or row['答案'] != original_row['答案']:
                changes = True

            if changes:  # 只有确实有变化时才保存
                try:
                    resp = api.edit_chat_history(
                        id=record_id,
                        query=row['问题'],
                        answer=row['答案'],
                    )
                    if resp['code'] == 200:
                        st.toast(f"成功更新记录 : {row['问题']}", icon="✅")
                        # 清除缓存以刷新数据
                        st.cache_data.clear()
                    else:
                        st.error(f"更新失败: {resp['msg']}")
                except Exception as e:
                    st.error(f"更新记录时出错: {str(e)}")

    # 更新选择状态
    if "选择" in edited_df.columns:
        st.session_state[f'selected_{st.session_state.chat_history_page}'] = edited_df["选择"].tolist()

    # Calculate total pages
    total_pages = max(1, (total_count + default_per_page - 1) // default_per_page)

    # Pagination controls with unique keys
    col1, col2, col3 = st.columns([5, 5, 5], gap="large")
    with col1:
        if st.button("◀ 上一页",
                     disabled=(st.session_state.chat_history_page == 1),
                     use_container_width=True,
                     key=f"prev_{st.session_state.chat_history_page}"):
            st.session_state.chat_history_page = max(1, st.session_state.chat_history_page - 1)
            st.rerun()

    with col2:
        # Current page info
        st.write(f"\n\t\r第 {st.session_state.chat_history_page}/{total_pages} 页 | 共 {total_count} 条记录")

    with col3:
        if st.button("下一页 ▶",
                     disabled=(st.session_state.chat_history_page >= total_pages),
                     use_container_width=True,
                     key=f"next_{st.session_state.chat_history_page}"):
            st.session_state.chat_history_page = min(total_pages, st.session_state.chat_history_page + 1)
            st.rerun()

    # 添加排序控制UI
    with st.sidebar:
        st.selectbox(
            "排序字段",
            options=[
                "add_time",
                "shop_name",
                "knowledge_base",
                "prompt_name",
                "query",
                "is_update_knowledge",
                "add_type",
            ],
            format_func=lambda x: {
                "add_time": "创建时间",
                "shop_name": "店铺名称",
                "knowledge_base": "知识库",
                "prompt_name": "客服模板",
                "query": "问题",
                "is_update_knowledge": "转换状态",
                "add_type": "添加类型",
            }.get(x, x),
            index=0,
            key="chat_sort_column_select",
            on_change=update_sort_and_rerun
        )
        st.selectbox(
            "排序方向",
            options=["desc", "asc"],
            format_func=lambda x: "降序" if x == "desc" else "升序",
            index=0,
            key="chat_sort_direction_select",
            on_change=update_sort_and_rerun
        )
        st.divider()

        # Get selected rows
        selected_rows = edited_df[edited_df["选择"]] if "选择" in edited_df.columns else pd.DataFrame()

        # Knowledge base selection
        kb_list = [x["kb_name"] for x in api.list_knowledge_bases()]
        selected_kb = st.selectbox(
            "聊天记录到知识库：",
            kb_list,
            index=0,
            key="selected_kb",
            help="将选中的聊天记录转换到指定的知识库"
        )

        # Action buttons
        if not selected_rows.empty:
            st.divider()
            st.success(f"已选择 {len(selected_rows)} 条记录")

    col = st.columns(2)
    if col[0].button("🚀 转换到知识库", type="primary", help="将选中的聊天记录添加到知识库", use_container_width=True):
        with st.spinner(f"正在转换 {len(selected_rows)} 条记录..."):
            try:
                id_list = selected_rows["ID"].tolist()
                resp = api.history_2_knowledge(id_list, selected_kb)
                if resp['code'] == 200:
                    st.toast("转换成功!", icon="✅")
                    st.success(f"成功转换 {len(id_list)} 条记录到知识库【{selected_kb}】,文件名：{resp['data']}")
                else:
                    st.error(f"转换失败: {resp['msg']}")
            except Exception as e:
                st.error(f"发生异常: {str(e)}")
                st.exception(e)

    if col[1].button("🔄 刷新当前页", help="重新加载当前页数据", use_container_width=True):
        st.cache_data.clear()
        st.rerun()

    # 新增：手动添加聊天记录的展开区域
    with st.expander("📝 手动添加聊天记录", expanded=False):
        with st.form("add_chat_form"):
            col1, col2 = st.columns(2)
            with col1:
                manual_query = st.text_area("问题/提问", height=100,
                                            help="输入用户的问题或提问内容")
            with col2:
                manual_answer = st.text_area("回答/答案", height=100,
                                             help="输入AI的回答内容")

            kb_list = [x["kb_name"] for x in api.list_knowledge_bases()]
            manual_kb = st.selectbox(
                "关联知识库",
                kb_list,
                index=0,
                help="选择与此对话关联的知识库"
            )

            submitted = st.form_submit_button("添加记录")
            if submitted:
                if not manual_query or not manual_answer:
                    st.error("问题和答案不能为空!")
                else:
                    try:
                        # 调用API添加记录
                        resp = api.manually_add_query(
                            query=manual_query,
                            answer=manual_answer,
                            knowledge_base=manual_kb,
                        )
                        if resp['code'] == 200:
                            st.toast("成功添加聊天记录! 1s后自动刷新", icon="✅")
                            time.sleep(1)
                            st.cache_data.clear()  # 清除缓存以刷新数据
                            st.rerun()
                        else:
                            st.error(f"添加失败: {resp['msg']}")
                    except Exception as e:
                        st.error(f"添加记录时出错: {str(e)}")

