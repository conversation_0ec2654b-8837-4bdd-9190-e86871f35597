import datetime
import time

import pandas as pd
import streamlit as st
from chatchat.webui_pages.utils import ApiRequest

def prompt_setting(api: ApiRequest):
    # prompt 模板管理
    st.subheader("客服模板")

    # Initialize default values
    default_per_page = 10

    # Initialize session state
    if 'prompt_page' not in st.session_state:
        st.session_state.prompt_page = 1
    if 'last_edited' not in st.session_state:
        st.session_state.last_edited = {}
    if 'show_add_form' not in st.session_state:
        st.session_state.show_add_form = False
    if 'selected_rows' not in st.session_state:
        st.session_state.selected_rows = set()

    if 'prompt_sort_column' not in st.session_state:
        st.session_state.prompt_sort_column = "update_time"  # 默认按创建时间排序
    if 'prompt_sort_direction' not in st.session_state:
        st.session_state.prompt_sort_direction = "desc"  # 默认降序

    # 获取数据函数
    def get_data(page, per_page, prompt_sort_column, sort_direction):
        res = api.get_prompt_template_list(sort_direction, prompt_sort_column, prompt_type='rag', page=page,
                                           per_page=per_page)
        value = []
        for v in res['prompt_list']:
            value.append({
                "id": v['id'],
                "选择": False,  # 添加选择列
                "模板类型": v['prompt_type'],
                "客服模板": v['prompt_name'],
                "模板内容": v['template'],
                "简介": v['description'],
                "更新时间": datetime.datetime.fromtimestamp(v['update_time']).strftime("%Y-%m-%d %H:%M:%S") if v.get(
                    'update_time') else '未知时间',
            })
        return value, res.get('total', 0)

    # 保存修改的函数
    def save_changes(changed_rows):
        with st.spinner(f"正在保存 {len(changed_rows)} 条修改..."):
            success_count = 0
            # Create a mapping of index to original ID
            index_id_map = original_df.set_index(original_df.index)['id'].to_dict()

            for index, row in changed_rows.iterrows():
                try:
                    original_id = index_id_map[index]
                    st.session_state.last_edited[original_id] = {
                        '客服模板': row['客服模板'],
                        '模板内容': row['模板内容'],
                        '简介': row['简介']
                    }

                    result = api.edit_prompt_template(
                        prompt_id=original_id,
                        prompt_type=original_df.loc[index, '模板类型'],
                        prompt_name=row['客服模板'],
                        template=row['模板内容'],
                        description=row['简介'],
                    )
                    if result.get('code') == 200:
                        success_count += 1
                    else:
                        st.error(f"更新失败: {result.get('msg', '未知错误')}")
                except Exception as e:
                    st.error(f"更新异常: {str(e)}")

            if success_count > 0:
                st.success(f"成功保存 {success_count}/{len(changed_rows)} 条修改")
                st.cache_data.clear()
                st.rerun()

    # 删除选择的模板函数
    def delete_selected_prompts(selected_ids):
        with st.spinner("正在删除选中的模板..."):
            success_count = 0
            try:
                result = api.del_prompt_template(selected_ids)
                if result.get('code') == 200:
                    success_count += 1
                else:
                    st.error(f"删除失败: {result.get('msg', '未知错误')}")
            except Exception as e:
                st.error(f"删除异常{str(e)}")

            if success_count > 0:
                st.success(f"成功删除 {success_count}/{len(selected_ids)} 条模板")
                st.cache_data.clear()
                st.session_state.selected_rows = set()  # 清空选择
                st.rerun()

    # 新增模板函数
    def add_new_prompt(prompt_name, template, description):
        with st.spinner("正在创建新模板..."):
            try:
                result = api.add_prompt_template(
                    prompt_type='rag',
                    prompt_name=prompt_name,
                    template=template,
                    description=description
                )
                if result.get('code') == 200:
                    st.toast("新增模板成功! 1s后自动刷新", icon="✅")
                    time.sleep(1)
                    st.cache_data.clear()  # 清除缓存以刷新数据
                    st.session_state.show_add_form = False
                    st.rerun()
                else:
                    st.error(f"新增失败: {result.get('message', '未知错误')}")
            except Exception as e:
                st.error(f"新增异常: {str(e)}")

    # 添加排序更新函数
    def update_sort_and_rerun():
        """更新排序参数并重新加载页面"""
        # 获取当前选择框的值
        sort_column = st.session_state.prompt_sort_column_select
        sort_direction = st.session_state.prompt_sort_direction_select

        # 只有当排序条件真正改变时才更新
        if (sort_column != st.session_state.prompt_sort_column or
                sort_direction != st.session_state.prompt_sort_direction):
            st.session_state.prompt_sort_column = sort_column
            st.session_state.prompt_sort_direction = sort_direction
            st.session_state.prompt_history_page = 1  # 排序后重置到第一页
            st.cache_data.clear()

    # 获取当前页数据
    data, total_count = get_data(
        st.session_state.prompt_page,
        default_per_page,
        st.session_state.prompt_sort_column,
        st.session_state.prompt_sort_direction
    )
    original_df = pd.DataFrame(data)

    # 应用之前保存的编辑状态
    if st.session_state.last_edited:
        for id, edits in st.session_state.last_edited.items():
            if id in original_df['id'].values:
                original_df.loc[original_df['id'] == id, '客服模板'] = edits['客服模板']
                original_df.loc[original_df['id'] == id, '模板内容'] = edits['模板内容']
                original_df.loc[original_df['id'] == id, '简介'] = edits['简介']

    # 显示数据编辑器
    edited_df = st.data_editor(
        original_df,
        column_config={
            "选择": st.column_config.CheckboxColumn("选择", help="选择要操作的模板", default=False),
            "id": st.column_config.Column(disabled=True),
            "模板类型": st.column_config.Column(disabled=True),
            "客服模板": st.column_config.TextColumn("客服模板名称"),  # 修改为可编辑的文本列
            "模板内容": st.column_config.TextColumn(width="large"),
            "简介": st.column_config.TextColumn(),
            "更新时间": st.column_config.Column(disabled=True)
        },
        column_order=["选择", "客服模板", "模板内容", "简介", "更新时间"],  # 只显示这些列
        hide_index=True,
        use_container_width=True,
        key=f"data_editor_{st.session_state.prompt_page}"
    )

    # 获取选择的行的ID
    if '选择' in edited_df.columns:
        selected_rows = edited_df[edited_df['选择']]
        selected_ids = selected_rows['id'].tolist()
    else:
        selected_ids = []

    # 分页控制
    total_pages = max(1, (total_count + default_per_page - 1) // default_per_page)

    col1, col2, col3 = st.columns([2, 3, 2])
    with col1:
        if st.button("◀ 上一页", disabled=(st.session_state.prompt_page == 1), use_container_width=True):
            st.session_state.prompt_page -= 1
            st.rerun()

    with col2:
        st.markdown(
            f"<div style='text-align: center;'>第 {st.session_state.prompt_page}/{total_pages} 页 | 共 {total_count} 条</div>",
            unsafe_allow_html=True)

    with col3:
        if st.button("下一页 ▶", disabled=(st.session_state.prompt_page >= total_pages), use_container_width=True):
            st.session_state.prompt_page += 1
            st.rerun()

    st.divider()

    # 操作按钮
    col1, col2, col3 = st.columns(3)
    with col1:
        if st.button("💾 保存所有修改", use_container_width=True):
            # 保存修改按钮
            changed_rows = edited_df[
                (edited_df['客服模板'] != original_df['客服模板']) |
                (edited_df['模板内容'] != original_df['模板内容']) |
                (edited_df['简介'] != original_df['简介'])
                ]

            if not changed_rows.empty:
                save_changes(changed_rows)
            else:
                st.warning("没有检测到任何修改")

    with col2:
        if st.button("➕ 新增模板", use_container_width=True):
            st.session_state.show_add_form = not st.session_state.show_add_form

    with col3:
        if st.button("🗑️ 删除选中", type="primary", disabled=len(selected_ids) == 0, use_container_width=True):
            delete_selected_prompts(selected_ids)

    # 新增模板表单
    if st.session_state.show_add_form:
        with st.form("add_prompt_form"):
            prompt_name = st.text_input("客服模板*")
            template = st.text_area("模板内容*", height=200,
                                    placeholder="请输入模板内容，可使用变量如{{变量名}}")
            description = st.text_input("简介", placeholder="可选描述")
            submitted = st.form_submit_button("确认新增")
            if submitted:
                if not all([prompt_name, template]):
                    st.error("带*的字段为必填项")
                else:
                    add_new_prompt(prompt_name, template, description)

    # 添加排序控制UI
    with st.sidebar:
        sort_column = st.selectbox(
            "排序字段",
            options=[
                "update_time",
                "prompt_name",
                "template",
            ],
            format_func=lambda x: {
                'update_time': "更新时间",
                'prompt_name': "客服模板",
                'template': "模板内容",
            }.get(x, x),
            index=0,
            key="prompt_sort_column_select",
            on_change = lambda: update_sort_and_rerun()  # 添加回调函数
        )
        sort_direction = st.selectbox(
            "排序方向",
            options=["desc", "asc"],
            format_func=lambda x: "降序" if x == "desc" else "升序",
            index=0,
            key="prompt_sort_direction_select",
            on_change=lambda: update_sort_and_rerun()  # 添加回调函数
        )
        st.session_state.prompt_sort_column = sort_column
        st.session_state.prompt_sort_direction = sort_direction
